# 企业微信动态配置重构方案

**作者**: hongdong.xie  
**日期**: 2025-08-13 16:33:44  
**版本**: v1.0

## 一、现状分析

通过代码分析，发现当前系统存在以下问题：

### 1.1 硬编码枚举限制
- **CompanyNoEnum** 枚举硬编码了企业信息（好买财富、好买基金、好晓买）
- **WechatApplicationEnum** 枚举硬编码了应用配置，与CompanyNoEnum强耦合
- 新增企业需要修改枚举代码并重新部署

### 1.2 使用CompanyNoEnum的关键位置
1. **配置服务层**：`BaseConfigServce`、`CacheBaseConfigServce`
2. **业务服务层**：`UserTagService`、`WechatCustInfoService`、`WechatGroupUserService`
3. **控制器层**：多个Controller硬编码默认值`CompanyNoEnum.HOWBUY_WEALTH`
4. **工具类**：`WechatCorpUtil`、`AbsWechatOuterService`
5. **外部服务**：各种微信API调用服务

### 1.3 数据库配置已就绪
- `cm_wechat_company` 表：企业微信账号配置
- `cm_wechat_application` 表：企业微信应用配置
- 缓存机制已实现：`CacheBaseConfigServce`

## 二、重构方案设计

### 2.1 总体架构设计

```mermaid
graph TD
    A[Controller层] --> B[Service层]
    B --> C[配置管理层]
    C --> D[缓存层]
    C --> E[数据库层]
    
    D --> F[企业配置缓存]
    D --> G[应用配置缓存]
    
    E --> H[cm_wechat_company]
    E --> I[cm_wechat_application]
    
    J[CompanyNoEnum] -.-> K[CompanyConfigManager]
    L[WechatApplicationEnum] -.-> M[ApplicationConfigManager]
    
    style J fill:#ffcccc
    style L fill:#ffcccc
    style K fill:#ccffcc
    style M fill:#ccffcc
```

### 2.2 核心设计原则

1. **向后兼容**：保留现有枚举，标记为@Deprecated，逐步迁移
2. **配置驱动**：所有企业和应用配置从数据库动态加载
3. **缓存优化**：利用现有缓存机制，提升性能
4. **统一管理**：创建统一的配置管理器
5. **默认策略**：保持现有默认值逻辑，确保兼容性

## 三、详细实现方案

### 3.1 第一阶段：创建动态配置管理器

#### 3.1.1 企业配置管理器

```java
/**
 * @description: 企业配置动态管理器
 * <AUTHOR>
 * @date 2025-08-13 16:33:44
 * @since JDK 1.8
 */
@Service
@Slf4j
public class CompanyConfigManager {
    
    @Autowired
    private CacheBaseConfigServce cacheBaseConfigServce;
    
    /**
     * 默认企业编码（向后兼容）
     */
    private static final String DEFAULT_COMPANY_NO = "1"; // HOWBUY_WEALTH
    
    /**
     * 获取所有有效企业配置
     */
    public List<CorpUtilityDTO> getAllCompanyConfigs() {
        Map<String, CorpUtilityDTO> configMap = cacheBaseConfigServce.getCorpConfigMap();
        return new ArrayList<>(configMap.values());
    }
    
    /**
     * 根据企业编码获取配置
     */
    public CorpUtilityDTO getCompanyConfig(String companyNo) {
        if (StringUtils.isBlank(companyNo)) {
            companyNo = DEFAULT_COMPANY_NO;
        }
        return cacheBaseConfigServce.getCorpConfig(companyNo);
    }
    
    /**
     * 验证企业编码是否有效
     */
    public boolean isValidCompanyNo(String companyNo) {
        return getCompanyConfig(companyNo) != null;
    }
    
    /**
     * 获取默认企业编码
     */
    public String getDefaultCompanyNo() {
        return DEFAULT_COMPANY_NO;
    }
}
```

#### 3.1.2 应用配置管理器

```java
/**
 * @description: 应用配置动态管理器
 * <AUTHOR>
 * @date 2025-08-13 16:33:44
 * @since JDK 1.8
 */
@Service
@Slf4j
public class ApplicationConfigManager {
    
    @Autowired
    private CacheBaseConfigServce cacheBaseConfigServce;
    
    /**
     * 应用类型：客户联系
     */
    private static final String APPLICATION_TYPE_CUSTOMER = "customer";
    
    /**
     * 根据企业编码获取客户联系应用配置
     */
    public ApplicationUtilityDTO getCustomerApplication(String companyNo) {
        Map<String, ApplicationUtilityDTO> configMap = cacheBaseConfigServce.getApplicationConfigMap();
        return configMap.values().stream()
                .filter(app -> companyNo.equals(app.getCompanyNo()))
                .filter(app -> APPLICATION_TYPE_CUSTOMER.equals(app.getApplicationType()))
                .findFirst()
                .orElse(null);
    }
    
    /**
     * 根据企业编码获取所有应用配置
     */
    public List<ApplicationUtilityDTO> getApplicationsByCompany(String companyNo) {
        Map<String, ApplicationUtilityDTO> configMap = cacheBaseConfigServce.getApplicationConfigMap();
        return configMap.values().stream()
                .filter(app -> companyNo.equals(app.getCompanyNo()))
                .collect(Collectors.toList());
    }
}
```

### 3.2 第二阶段：重构BaseConfigServce

#### 3.2.1 添加基于companyNo的方法

```java
// 新增方法，基于companyNo字符串
public String getCorpId(String companyNo) {
    CorpUtilityDTO utilityDTO = getCorpUtilityDTO(companyNo);
    if (utilityDTO == null) {
        throw new BusinessException(ResponseCodeEnum.SYS_CONFIG_ERROR);
    }
    return utilityDTO.getCorpId();
}

public CorpUtilityDTO getCorpUtilityDTO(String companyNo) {
    if (StringUtils.isBlank(companyNo)) {
        companyNo = "1"; // 默认好买财富
    }
    CorpUtilityDTO utilityDTO = cacheBaseConfigServce.getCorpConfig(companyNo);
    if (utilityDTO == null) {
        log.error("企业配置不存在，companyNo: {}", companyNo);
        throw new BusinessException(ResponseCodeEnum.SYS_CONFIG_ERROR);
    }
    return utilityDTO;
}

public ApplicationUtilityDTO getCustApplicationUtilityDTO(String companyNo) {
    Map<String, ApplicationUtilityDTO> cacheMap = cacheBaseConfigServce.getApplicationConfigMap();
    List<ApplicationUtilityDTO> custAppList = cacheMap.values().stream()
            .filter(app -> companyNo.equals(app.getCompanyNo()))
            .filter(app -> APPLICATION_TYPE_CUSTOMER.equals(app.getApplicationType()))
            .collect(Collectors.toList());
    
    if (CollectionUtils.isEmpty(custAppList)) {
        log.error("企业客户联系应用配置不存在，companyNo: {}", companyNo);
        return null;
    }
    return custAppList.get(0);
}
```

### 3.3 第三阶段：创建统一的参数处理工具

#### 3.3.1 企业参数处理工具类

```java
/**
 * @description: 企业编码处理工具类
 * <AUTHOR>
 * @date 2025-08-13 16:33:44
 * @since JDK 1.8
 */
@Slf4j
public class CompanyNoUtils {
    
    /**
     * 默认企业编码（好买财富）
     */
    private static final String DEFAULT_COMPANY_NO = "1";
    
    /**
     * 从请求中获取企业编码，支持默认值
     */
    public static String getCompanyNo(BaseCompanyNoRequest request) {
        return getCompanyNo(request.getCompanyNo());
    }
    
    /**
     * 处理企业编码，支持默认值和兼容性
     */
    public static String getCompanyNo(String companyNo) {
        if (StringUtils.isBlank(companyNo)) {
            log.warn("企业编码为空，使用默认值: {}", DEFAULT_COMPANY_NO);
            return DEFAULT_COMPANY_NO;
        }
        return companyNo;
    }
    
    /**
     * 兼容CompanyNoEnum的转换方法
     */
    @Deprecated
    public static String fromCompanyNoEnum(CompanyNoEnum companyNoEnum) {
        if (companyNoEnum == null) {
            log.warn("CompanyNoEnum为空，使用默认值: {}", DEFAULT_COMPANY_NO);
            return DEFAULT_COMPANY_NO;
        }
        return companyNoEnum.getCode();
    }
    
    /**
     * 兼容转换为CompanyNoEnum（逐步废弃）
     */
    @Deprecated
    public static CompanyNoEnum toCompanyNoEnum(String companyNo) {
        companyNo = getCompanyNo(companyNo);
        CompanyNoEnum enumValue = CompanyNoEnum.getEnum(companyNo);
        if (enumValue == null) {
            log.warn("无法转换为CompanyNoEnum，companyNo: {}，使用默认值", companyNo);
            return CompanyNoEnum.HOWBUY_WEALTH;
        }
        return enumValue;
    }
}
```

### 3.4 第四阶段：重构Service层

#### 3.4.1 修改BaseConfigServce兼容性

```java
// 保留原有方法，标记为废弃，内部调用新方法
@Deprecated
public String getCorpId(CompanyNoEnum companyNoEnum) {
    return getCorpId(CompanyNoUtils.fromCompanyNoEnum(companyNoEnum));
}

@Deprecated
public CorpUtilityDTO getCorpUtilityDTO(CompanyNoEnum companyNoEnum) {
    return getCorpUtilityDTO(CompanyNoUtils.fromCompanyNoEnum(companyNoEnum));
}

@Deprecated
public WechatApplicationEnum getCustApplicationEnum(CompanyNoEnum companyNoEnum) {
    String companyNo = CompanyNoUtils.fromCompanyNoEnum(companyNoEnum);
    ApplicationUtilityDTO appConfig = getCustApplicationUtilityDTO(companyNo);
    if (appConfig == null) {
        return null;
    }
    // 兼容性转换
    return WechatApplicationEnum.getEnum(appConfig.getApplicationCode());
}
```

#### 3.4.2 重构业务Service类

```java
// 新增基于companyNo的方法
private static String getCompanyNo(BaseCompanyNoRequest request) {
    return CompanyNoUtils.getCompanyNo(request);
}

// 保留原有方法，标记废弃
@Deprecated
private static CompanyNoEnum getCompanyNoEnum(BaseCompanyNoRequest request) {
    return CompanyNoUtils.toCompanyNoEnum(request.getCompanyNo());
}
```

### 3.5 第五阶段：重构Controller层

#### 3.5.1 统一Controller参数处理

```java
@PostMapping("/selectrelationlistbyvo")
public List<CustConsultRelationPO> selectRelationListByVo(
        @RequestBody List<CustConsultRelationVO> voList,
        @RequestParam(required = false) String companyNo) {

    // 使用新的工具类处理企业编码
    String processedCompanyNo = CompanyNoUtils.getCompanyNo(companyNo);
    return wechatCustRelationService.selectRelationListByVo(processedCompanyNo, voList);
}
```

#### 3.5.2 修改其他Controller

```java
@PostMapping("/getgroupidbyhboneno")
public Response<GroupIdVO> getGroupIdByHbOneNo(
        String hbOneNo,
        @RequestParam(required = false) String companyNo) {

    String processedCompanyNo = CompanyNoUtils.getCompanyNo(companyNo);
    GroupIdVO vo = wechatGroupUserService.queryNormalByHboneNo(processedCompanyNo, hbOneNo);
    return Response.ok(vo);
}
```

### 3.6 第六阶段：重构工具类

#### 3.6.1 重构WechatCorpUtil

```java
@Autowired
private static ApplicationConfigManager applicationConfigManager;

/**
 * 根据企业编码获取发送消息的应用配置
 */
public static ApplicationUtilityDTO getSendMsgApp(String companyNo) {
    // 从数据库配置中获取默认发送消息的应用
    List<ApplicationUtilityDTO> apps = applicationConfigManager.getApplicationsByCompany(companyNo);
    return apps.stream()
            .filter(app -> "message".equals(app.getApplicationType()) || "crm".equals(app.getApplicationType()))
            .findFirst()
            .orElse(null);
}

// 保留原有方法，标记废弃
@Deprecated
public static WechatApplicationEnum getSendMsgAppEnum(CompanyNoEnum companyNoEnum) {
    String companyNo = CompanyNoUtils.fromCompanyNoEnum(companyNoEnum);
    ApplicationUtilityDTO appConfig = getSendMsgApp(companyNo);
    if (appConfig == null) {
        return null;
    }
    return WechatApplicationEnum.getEnum(appConfig.getApplicationCode());
}
```

## 四、数据库配置增强

### 4.1 企业配置表增强

```sql
-- 为cm_wechat_company表添加索引
ALTER TABLE cm_wechat_company ADD INDEX idx_company_no (company_no);
ALTER TABLE cm_wechat_company ADD INDEX idx_corp_id (corp_id);

-- 为cm_wechat_application表添加索引
ALTER TABLE cm_wechat_application ADD INDEX idx_company_no (company_no);
ALTER TABLE cm_wechat_application ADD INDEX idx_application_code (application_code);
ALTER TABLE cm_wechat_application ADD INDEX idx_application_type (application_type);

-- 添加默认发送消息应用类型字段
ALTER TABLE cm_wechat_application ADD COLUMN is_default_msg_app TINYINT(1) DEFAULT 0 COMMENT '是否为默认消息发送应用:0-否 1-是';
```

### 4.2 配置数据初始化

```sql
-- 确保现有企业配置数据完整
INSERT INTO cm_wechat_company (company_no, company_desc, corp_type, corp_id, token, encoding_aes_key, creator, create_time, rec_stat)
VALUES
('1', '好买财富', '1', 'your_wealth_corp_id', 'your_wealth_token', 'your_wealth_aes_key', 'system', NOW(), '1'),
('2', '好买基金', '1', 'your_fund_corp_id', 'your_fund_token', 'your_fund_aes_key', 'system', NOW(), '1'),
('3', '好晓买', '1', 'your_hxm_corp_id', 'your_hxm_token', 'your_hxm_aes_key', 'system', NOW(), '1')
ON DUPLICATE KEY UPDATE modify_time = NOW();

-- 设置默认消息发送应用
UPDATE cm_wechat_application SET is_default_msg_app = 1
WHERE application_code IN ('wealth_crm', 'fund_customer', 'hxm_customer');
```

## 五、迁移计划

### 5.1 迁移阶段规划

| 阶段 | 内容 | 时间估算 | 风险等级 |
|------|------|----------|----------|
| 第一阶段 | 创建配置管理器 | 1天 | 低 |
| 第二阶段 | 重构BaseConfigServce | 1天 | 中 |
| 第三阶段 | 创建工具类 | 0.5天 | 低 |
| 第四阶段 | 重构Service层 | 2天 | 中 |
| 第五阶段 | 重构Controller层 | 2天 | 高 |
| 第六阶段 | 重构工具类 | 1天 | 中 |
| 第七阶段 | 测试验证 | 2天 | 中 |

### 5.2 向后兼容策略

1. **保留现有枚举**：CompanyNoEnum和WechatApplicationEnum标记为@Deprecated
2. **双重支持**：新旧方法并存，逐步迁移
3. **默认值保持**：确保现有默认逻辑不变
4. **日志监控**：添加迁移过程日志，便于问题排查

### 5.3 测试策略

1. **单元测试**：每个新增配置管理器的方法
2. **集成测试**：验证缓存机制正常工作
3. **兼容性测试**：确保现有API行为不变
4. **性能测试**：验证配置查询性能
5. **回归测试**：全量业务功能验证

## 六、预期收益

### 6.1 功能收益
- ✅ 支持动态添加新企业微信账号，无需修改代码
- ✅ 支持动态配置企业微信应用，灵活性大幅提升
- ✅ 统一配置管理，降低维护成本

### 6.2 技术收益
- ✅ 消除硬编码，提升代码质量
- ✅ 配置驱动架构，符合最佳实践
- ✅ 向后兼容，平滑迁移

### 6.3 业务收益
- ✅ 快速支持新企业接入
- ✅ 降低运维成本
- ✅ 提升系统扩展性

## 七、风险评估与应对

### 7.1 技术风险

| 风险项 | 风险等级 | 影响 | 应对措施 |
|--------|----------|------|----------|
| 缓存一致性 | 中 | 配置更新延迟 | 增加缓存刷新机制 |
| 性能影响 | 低 | 查询性能下降 | 优化索引，监控性能 |
| 兼容性问题 | 中 | 现有功能异常 | 充分测试，保留回滚方案 |

### 7.2 业务风险

| 风险项 | 风险等级 | 影响 | 应对措施 |
|--------|----------|------|----------|
| 配置错误 | 高 | 业务功能异常 | 配置验证机制，操作审计 |
| 数据迁移 | 中 | 历史数据丢失 | 数据备份，分步迁移 |

### 7.3 回滚方案

1. **代码回滚**：保留原有枚举和方法，可快速切换
2. **配置回滚**：数据库配置支持版本管理
3. **缓存回滚**：支持手动刷新缓存到指定版本

## 八、总结

本方案通过渐进式重构，将硬编码的CompanyNoEnum枚举改为基于数据库的动态配置，实现了以下目标：

1. **解决核心问题**：支持动态添加新企业微信账号，无需修改代码
2. **保证系统稳定**：向后兼容，平滑迁移，风险可控
3. **提升架构质量**：配置驱动，消除硬编码，符合最佳实践
4. **增强业务敏捷性**：快速支持新企业接入，降低运维成本

该方案经过充分的技术分析和风险评估，具有较强的可操作性和实用性，建议按计划逐步实施。
